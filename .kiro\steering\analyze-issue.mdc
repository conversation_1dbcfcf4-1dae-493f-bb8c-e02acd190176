---
type: "always_apply"
description: "GitHub issue analysis and implementation specification - GitHub问题分析和实现规范"
---
# 🔍 Issue Analysis - 问题分析

分析GitHub问题并创建详细的实现规范。

## 🚀 Commands - 命令

- `/analyze-issue` - 分析GitHub问题
- `/issue-spec` - 创建实现规范
- `/estimate` - 评估工作量

## 📋 Process - 分析流程

### 1. 🔍 问题理解 (Issue Understanding)
- 仔细阅读问题描述和评论
- 识别核心需求和约束条件
- 理解用户期望和使用场景
- **转换条件**：问题理解清晰，需求明确

### 2. 📊 技术分析 (Technical Analysis)
- 分析现有代码库结构
- 识别相关模块和依赖
- 评估技术可行性
- **转换条件**：技术方案可行，风险可控

### 3. 📝 实现规范 (Implementation Spec)
- 创建详细的实现计划
- 定义接口和数据结构
- 规划测试策略
- **转换条件**：规范完整，可执行性强

### 4. ⏱️ 工作量评估 (Effort Estimation)
- 评估开发时间
- 识别潜在风险
- 制定里程碑计划
- **转换条件**：评估合理，计划可行

## 🛠️ Analysis Framework - 分析框架

### Issue Classification - 问题分类
- **Bug** 🐛 - 功能缺陷
- **Feature** ✨ - 新功能需求
- **Enhancement** ⚡ - 功能改进
- **Documentation** 📝 - 文档相关
- **Performance** 🚀 - 性能优化
- **Security** 🔒 - 安全问题

### Priority Assessment - 优先级评估
- **Critical** 🔴 - 阻塞性问题
- **High** 🟠 - 重要功能
- **Medium** 🟡 - 一般需求
- **Low** 🟢 - 优化建议

### Complexity Analysis - 复杂度分析
- **Simple** - 1-2天
- **Medium** - 3-5天
- **Complex** - 1-2周
- **Epic** - 2周以上

## 📋 Implementation Template - 实现模板

### 问题分析报告
```markdown
## 问题分析

### 核心需求
- [ ] 需求1：描述
- [ ] 需求2：描述

### 技术要求
- [ ] 技术要求1
- [ ] 技术要求2

### 约束条件
- [ ] 约束1
- [ ] 约束2

## 实现方案

### 架构设计
- 模块结构
- 数据流设计
- 接口定义

### 实现步骤
1. [ ] 步骤1
2. [ ] 步骤2
3. [ ] 步骤3

### 测试策略
- [ ] 单元测试
- [ ] 集成测试
- [ ] 用户验收测试

## 工作量评估

- **预估时间**：X天
- **风险等级**：低/中/高
- **依赖项**：列出依赖
```

## ✅ Best Practices - 最佳实践

### 分析原则
- **全面性** - 考虑所有相关因素
- **准确性** - 基于事实和数据
- **可行性** - 方案切实可行
- **可测试性** - 结果可验证

### 沟通策略
- **及时反馈** - 定期更新进展
- **主动澄清** - 遇到歧义及时确认
- **文档记录** - 重要决策要记录
- **风险预警** - 提前识别和沟通风险

## 🔧 Tools Integration - 工具集成

### GitHub CLI
```bash
# 获取问题详情
gh issue view <issue-number>

# 创建分支
gh issue develop <issue-number>

# 关联PR
gh pr create --title "Fix #<issue-number>"
```

### 项目管理
- 创建项目看板
- 设置里程碑
- 分配任务标签
- 跟踪进度

## 📋 Checklist - 检查清单

### 分析阶段
- [ ] 问题描述理解清晰
- [ ] 技术可行性确认
- [ ] 资源需求评估完成
- [ ] 风险识别和缓解方案

### 规范阶段
- [ ] 实现方案详细完整
- [ ] 接口设计合理
- [ ] 测试策略覆盖全面
- [ ] 文档更新计划

### 评估阶段
- [ ] 工作量评估合理
- [ ] 时间计划可行
- [ ] 依赖关系明确
- [ ] 验收标准清晰
