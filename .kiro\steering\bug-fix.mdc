---
type: "always_apply"
description: "Complete bug-fixing workflow from issue to PR - 从问题到PR的完整Bug修复工作流"
---
# 🐛 Bug Fix Workflow - Bug修复工作流

从问题创建到拉取请求的完整Bug修复工作流程。

## 🚀 Commands - 命令

- `/bug-fix` - 启动Bug修复工作流
- `/reproduce-bug` - 重现Bug
- `/root-cause` - 根因分析
- `/fix-verify` - 修复验证

## 📋 Process - 修复流程

### 🔍 Before Starting - 开始前准备

#### 1. GitHub Issue Creation - 创建GitHub问题
```markdown
**Bug标题**: [组件/模块] 简短描述性标题

**环境信息**:
- 操作系统: Windows 11 / macOS 13 / Ubuntu 22.04
- 浏览器: Chrome 120 / Firefox 121 / Safari 17
- 应用版本: v2.1.0
- Node.js版本: v18.19.0

**重现步骤**:
1. 打开应用
2. 点击登录按钮
3. 输入有效凭据
4. 点击提交

**预期行为**: 用户应该成功登录并跳转到仪表板

**实际行为**: 显示"网络错误"，用户停留在登录页面

**错误信息**:
```
TypeError: Cannot read property 'token' of undefined
  at LoginComponent.handleLogin (login.component.ts:45)
```

**截图/录屏**: [附加相关截图或录屏]

**优先级**: High / Medium / Low
**标签**: bug, frontend, authentication
```

#### 2. Git Branch Creation - 创建Git分支
```bash
# 创建并切换到功能分支
git checkout -b fix/login-token-error

# 或者使用更具描述性的分支名
git checkout -b fix/issue-123-login-authentication-error
```

### 🔧 Fix the Bug - 修复Bug

#### Step 1: Reproduce the Issue - 重现问题
```bash
# 1. 切换到问题分支
git checkout main
git pull origin main

# 2. 尝试重现Bug
npm start
# 按照问题描述的步骤操作

# 3. 确认问题存在
# 记录重现步骤和错误信息
```

#### Step 2: Write Failing Test - 编写失败测试
```javascript
// 示例：为Bug编写测试用例
describe('LoginComponent', () => {
  it('should handle login with valid credentials', async () => {
    // 这个测试应该失败，因为Bug存在
    const mockUser = { username: 'test', password: 'password' };
    const result = await loginComponent.handleLogin(mockUser);
    
    expect(result.success).toBe(true);
    expect(result.token).toBeDefined();
    expect(result.user).toEqual(expect.objectContaining({
      username: 'test'
    }));
  });

  it('should handle undefined response gracefully', async () => {
    // 测试边界条件
    mockApiService.login.mockResolvedValue(undefined);
    
    const result = await loginComponent.handleLogin(mockUser);
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
});
```

#### Step 3: Implement the Fix - 实现修复
```typescript
// 修复前的代码
async handleLogin(credentials: LoginCredentials) {
  try {
    const response = await this.authService.login(credentials);
    // Bug: 没有检查response是否存在
    localStorage.setItem('token', response.token);
    this.router.navigate(['/dashboard']);
  } catch (error) {
    this.showError('登录失败');
  }
}

// 修复后的代码
async handleLogin(credentials: LoginCredentials) {
  try {
    const response = await this.authService.login(credentials);
    
    // 修复: 添加响应验证
    if (!response || !response.token) {
      throw new Error('Invalid response from server');
    }
    
    localStorage.setItem('token', response.token);
    this.router.navigate(['/dashboard']);
    return { success: true, token: response.token, user: response.user };
  } catch (error) {
    console.error('Login error:', error);
    this.showError('登录失败: ' + error.message);
    return { success: false, error: error.message };
  }
}
```

#### Step 4: Verify Test Passes - 验证测试通过
```bash
# 运行特定测试
npm test -- --grep "LoginComponent"

# 运行所有测试
npm test

# 确保测试通过
✓ should handle login with valid credentials
✓ should handle undefined response gracefully
```

#### Step 5: Run Full Test Suite - 运行完整测试套件
```bash
# 运行所有测试确保没有回归
npm run test:all

# 运行端到端测试
npm run test:e2e

# 运行代码检查
npm run lint

# 运行类型检查
npm run type-check
```

### ✅ On Completion - 完成后操作

#### 1. Git Commit - Git提交
```bash
# 暂存变更
git add .

# 提交变更，引用问题编号
git commit -m "🐛 fix(auth): 修复登录时token未定义错误 (#123)

- 添加响应验证防止undefined错误
- 改进错误处理和用户反馈
- 添加边界条件测试用例

修复了当服务器返回空响应时的崩溃问题
现在会显示有意义的错误消息给用户

Fixes #123"
```

#### 2. Push Branch - 推送分支
```bash
# 推送分支到远程仓库
git push origin fix/login-token-error
```

#### 3. Create Pull Request - 创建拉取请求
```markdown
## 🐛 Bug Fix: 修复登录时token未定义错误

### 问题描述
修复 #123 - 用户登录时遇到"Cannot read property 'token' of undefined"错误

### 根本原因
当认证服务返回空响应或格式不正确的响应时，代码没有进行适当的验证就尝试访问`token`属性。

### 解决方案
- ✅ 添加响应验证逻辑
- ✅ 改进错误处理机制
- ✅ 添加有意义的用户反馈
- ✅ 增加边界条件测试

### 测试
- [x] 单元测试通过
- [x] 集成测试通过
- [x] 手动测试验证
- [x] 回归测试通过

### 影响范围
- 仅影响登录组件
- 向后兼容
- 无破坏性变更

### 截图/录屏
[添加修复后的截图或录屏]

### 检查清单
- [x] 代码审查完成
- [x] 测试覆盖率充足
- [x] 文档已更新
- [x] 无安全风险
```

## 🔍 Root Cause Analysis - 根因分析

### Five Whys Technique - 五个为什么技术
```
1. 为什么登录失败？
   → 因为代码尝试访问undefined对象的token属性

2. 为什么response是undefined？
   → 因为API服务在某些情况下返回空响应

3. 为什么API服务返回空响应？
   → 因为网络超时或服务器错误时没有正确处理

4. 为什么没有验证响应？
   → 因为开发时假设API总是返回有效响应

5. 为什么没有考虑错误情况？
   → 因为缺少错误处理的最佳实践指导
```

### Prevention Measures - 预防措施
- 添加响应验证模式
- 实施错误处理标准
- 增加边界条件测试
- 代码审查检查清单更新

## 📊 Bug Categories - Bug分类

### Severity Levels - 严重程度
- **Critical** - 系统崩溃，数据丢失
- **High** - 核心功能无法使用
- **Medium** - 功能受限，有变通方案
- **Low** - 界面问题，不影响功能

### Bug Types - Bug类型
- **Logic Error** - 逻辑错误
- **Runtime Error** - 运行时错误
- **UI/UX Issue** - 界面/体验问题
- **Performance** - 性能问题
- **Security** - 安全漏洞
- **Compatibility** - 兼容性问题

## 🛠️ Tools and Techniques - 工具和技术

### Debugging Tools - 调试工具
```bash
# 浏览器开发者工具
F12 → Console/Network/Sources

# Node.js调试
node --inspect-brk app.js

# VS Code调试配置
{
  "type": "node",
  "request": "launch",
  "name": "Debug App",
  "program": "${workspaceFolder}/src/index.js"
}
```

### Logging and Monitoring - 日志和监控
```javascript
// 结构化日志
console.log('Login attempt', {
  timestamp: new Date().toISOString(),
  userId: credentials.username,
  userAgent: navigator.userAgent,
  error: error.message
});

// 错误监控集成
import * as Sentry from '@sentry/browser';
Sentry.captureException(error);
```

## ✅ Best Practices - 最佳实践

### Bug Prevention - Bug预防
- **代码审查** - 所有代码变更都要审查
- **测试驱动** - 先写测试，后写代码
- **静态分析** - 使用ESLint、TypeScript等工具
- **持续集成** - 自动化测试和部署

### Bug Fixing - Bug修复
- **重现优先** - 确保能稳定重现Bug
- **最小变更** - 只修改必要的代码
- **测试覆盖** - 为Bug添加测试用例
- **文档更新** - 更新相关文档

### Communication - 沟通
- **及时更新** - 定期更新Issue状态
- **清晰描述** - 详细描述问题和解决方案
- **知识分享** - 分享修复经验和教训
