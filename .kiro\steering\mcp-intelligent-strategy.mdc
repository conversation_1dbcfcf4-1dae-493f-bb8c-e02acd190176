---
description: "MCP工具智能使用强制策略 - 确保所有AI实例都具备高效工具使用能力"
globs: ["**/*"]
alwaysApply: true
---

# 🚨 MCP工具智能使用强制策略

**⚠️ 重要：这是强制性规则，所有AI实例必须遵循！**

## 🎯 核心强制原则

### 1. 任务开始时必须执行的检查
```
每个任务开始时，AI必须：
1. 立即扫描所有可用的MCP工具
2. 识别任务类型和复杂度
3. 评估MCP工具vs通用方法的效率
4. 选择最优方案执行
```

### 2. 强制性工具优先级
```
优先级排序（必须按此顺序考虑）：
1. 专用MCP工具（如github、memory、codebase-retrieval）
2. 通用MCP工具（如文件操作工具）
3. 通用方法（仅作为最后备选）
```

### 3. 强制性切换触发条件
```
以下情况必须重新评估工具选择：
- 通用方法连续失败2次以上
- 发现任务复杂度超出当前方法能力
- 识别到更优的MCP工具
- 用户反馈当前方法效率低下
```

### 4. 缺失工具的强制处理
```
当识别到需要但缺失的MCP工具时，必须：
1. 明确告知用户缺少的工具
2. 说明工具的具体价值和效率提升
3. 提供详细的配置指导
4. 主动协助用户完成配置
```

## 📋 强制性工具映射表

| 任务类型 | 必须首选的MCP工具 | 禁止的低效方法 |
|----------|-------------------|----------------|
| 文件操作 | `server-filesystem` 或文件系统MCP工具 | 逐个手动操作 |
| GitHub操作 | `server-github` 或GitHub MCP工具 | web-fetch抓取HTML |
| 代码检索 | `codebase-retrieval` 或代码检索MCP工具 | 盲目搜索文件 |
| 用户反馈 | `mcp-feedback-enhanced` 或反馈MCP工具 | 简单文本询问 |
| 记忆管理 | `server-memory` 或记忆MCP工具 | 临时变量存储 |
| 数据库操作 | `server-postgres`, `server-mysql` 等 | 手动SQL操作 |

## 🔄 强制性执行流程

```
任务接收 → [强制]扫描MCP工具 → [强制]效率评估 → [强制]选择最优方案 → 执行监控 → [强制]动态调整
```

## 🛠️ 具体执行指令

### 文件操作任务
```
IF 任务涉及文件读写 THEN
  首选：server-filesystem 或已配置的文件系统MCP工具
  检测：扫描可用的文件操作MCP工具（如 d-frontend-files, e-vue-projects 等）
  禁止：直接使用通用文件操作
  切换条件：MCP工具不可用或功能不足
```

### GitHub相关任务
```
IF 任务涉及GitHub操作 THEN
  首选：server-github 或已配置的GitHub MCP工具
  检测：扫描可用的GitHub MCP工具（如 github 等）
  禁止：使用web-fetch抓取GitHub页面
  切换条件：需要的功能GitHub工具不支持
```

### 代码分析任务
```
IF 任务需要理解或搜索代码 THEN
  首选：codebase-retrieval MCP工具
  禁止：手动逐文件搜索
  切换条件：代码库过大或工具不可用
```

### 用户交互任务
```
IF 任务需要用户反馈或确认 THEN
  首选：mcp-feedback-enhanced MCP工具
  禁止：简单的文本询问
  切换条件：需要复杂交互或工具不可用
```

## 🚨 违规检测

### 禁止的低效行为
- ❌ 有MCP工具可用时使用通用方法
- ❌ 连续失败2次以上不切换工具
- ❌ 发现缺失有用工具时不告知用户
- ❌ 不主动评估工具效率

### 必须的高效行为
- ✅ 任务开始时立即扫描MCP工具
- ✅ 优先选择最适合的MCP工具
- ✅ 遇到问题时主动重新评估工具选择
- ✅ 主动建议配置缺失的有用工具

## 📊 效果验证

### 自检清单
每个任务完成后，AI必须自问：
- [ ] 我是否在任务开始时扫描了MCP工具？
- [ ] 我是否选择了最优的工具方案？
- [ ] 我是否在遇到问题时重新评估了工具选择？
- [ ] 我是否主动建议了有用的缺失工具？

### 用户反馈指标
- 任务完成效率是否明显提升？
- 错误率是否显著降低？
- 用户是否感受到AI的智能化提升？

---

**🎯 记住：这不是建议，而是必须严格遵循的强制性策略！**

**🚀 目标：让每个使用这个项目的AI都成为高效的MCP工具使用专家！**
