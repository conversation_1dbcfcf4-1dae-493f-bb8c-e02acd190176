{"title": "AI小说IDE (NovelCraft AI Studio)", "features": ["模块化工作区系统 (5个)", "全局酒馆控制系统 (SillyTavern)", "SillyTavern扩展生态系统", "智能知识库系统 (本地向量搜索+RAG)", "知识图谱可视化系统 (Graphiti MCP)", "多章节改编引擎", "AI工作流引擎 (可视化节点编辑器)"], "tech": {"Desktop": "<PERSON><PERSON>", "Frontend": {"arch": "react", "component": "shadcn", "description": "使用React (TypeScript) 和 shadcn/ui 构建界面，Tailwind CSS 负责样式，Zustand 管理状态。"}, "Backend": {"language": "Rust", "description": "使用Rust处理高性能任务，如管理子进程、实现本地向量搜索和执行AI工作流。"}}, "design": "采用灵感源自VS Code和Obsidian的现代IDE暗色主题。界面以深灰色为背景，科技蓝为强调色，通过辉光和细微边框提升精致感。整体布局为模块化多工作区结构，左侧为固定图标导航，每个工作区采用独立的三栏式布局，确保信息架构清晰，交互体验专注高效。", "plan": {"1. 项目初始化与基础架构搭建": "holding", "2. 模块化工作区UI框架实现": "holding", "3. 实现核心写作工作区与富文本编辑器": "holding", "4. 集成SillyTavern并构建酒馆工作区": "holding", "5. 开发后端Rust模块：本地向量数据库与RAG接口": "holding", "6. 实现前端智能知识库系统与AI助手面板": "holding", "7. 集成知识图谱可视化库并构建思维导图工作区": "holding", "8. 开发多章节改编引擎的核心逻辑与UI": "holding", "9. 使用React Flow实现可视化AI工作流引擎": "holding", "10. 完善设置、主题、国际化等全局功能": "holding", "11. 进行整体联调测试、性能优化与打包发布": "holding"}}