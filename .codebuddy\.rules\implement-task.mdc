---
type: "always_apply"
description: "Methodical task implementation approach - 系统化任务实现方法"
---
# 🎯 Task Implementation - 任务实现

系统化的任务实现方法，确保高质量交付。

## 🚀 Commands - 命令

- `/implement-task` - 开始任务实现
- `/task-plan` - 制定实现计划
- `/task-review` - 任务审查
- `/task-complete` - 完成任务

## 📋 Process - 实现流程

### 1. 📊 任务分析 (Task Analysis)
- 理解任务需求和目标
- 分析技术要求和约束
- 识别依赖和风险
- **转换条件**：需求清晰，方案可行

### 2. 📝 实现规划 (Implementation Planning)
- 制定详细实现计划
- 分解子任务和里程碑
- 分配资源和时间
- **转换条件**：计划完整，可执行

### 3. 🔨 代码实现 (Code Implementation)
- 按计划实现功能
- 遵循代码规范
- 编写测试用例
- **转换条件**：功能完成，测试通过

### 4. ✅ 质量保证 (Quality Assurance)
- 代码审查和重构
- 性能优化
- 文档更新
- **转换条件**：质量达标，文档完整

### 5. 🚀 交付部署 (Delivery & Deployment)
- 集成测试
- 部署到环境
- 用户验收
- **转换条件**：验收通过，部署成功

## 🛠️ Implementation Strategy - 实现策略

### 分解原则
- **垂直切分** - 按功能模块分解
- **水平切分** - 按技术层次分解
- **优先级排序** - 核心功能优先
- **依赖管理** - 合理安排依赖顺序

### 开发模式
- **TDD** - 测试驱动开发
- **BDD** - 行为驱动开发
- **DDD** - 领域驱动设计
- **敏捷开发** - 迭代增量

### 质量控制
- **代码审查** - Peer Review
- **自动化测试** - 单元/集成测试
- **静态分析** - 代码质量检查
- **性能监控** - 运行时监控

## 📋 Task Template - 任务模板

### 任务实现计划
```markdown
## 任务概述

**任务名称**：[任务名称]
**优先级**：高/中/低
**预估工期**：X天
**负责人**：[姓名]

## 需求分析

### 功能需求
- [ ] 需求1：描述
- [ ] 需求2：描述

### 非功能需求
- [ ] 性能要求
- [ ] 安全要求
- [ ] 可用性要求

## 技术方案

### 架构设计
- 系统架构图
- 模块划分
- 接口设计

### 技术选型
- 框架/库选择
- 工具链配置
- 环境要求

## 实现计划

### 里程碑
1. [ ] 里程碑1 (Day 1-2)
2. [ ] 里程碑2 (Day 3-4)
3. [ ] 里程碑3 (Day 5-6)

### 风险识别
- 风险1：描述 + 缓解方案
- 风险2：描述 + 缓解方案

## 验收标准

### 功能验收
- [ ] 功能1测试通过
- [ ] 功能2测试通过

### 质量验收
- [ ] 代码覆盖率 > 80%
- [ ] 性能指标达标
- [ ] 安全扫描通过
```

## ✅ Best Practices - 最佳实践

### 实现原则
- **小步快跑** - 频繁提交和集成
- **持续反馈** - 及时沟通和调整
- **质量优先** - 不妥协代码质量
- **文档同步** - 代码和文档同步更新

### 协作方式
- **每日站会** - 同步进展和问题
- **代码审查** - 知识分享和质量保证
- **结对编程** - 复杂问题协作解决
- **技术分享** - 经验总结和传播

### 工具使用
- **版本控制** - Git工作流
- **项目管理** - 看板和燃尽图
- **自动化** - CI/CD流水线
- **监控告警** - 运行时监控

## 🔧 Tools Integration - 工具集成

### 开发工具
```bash
# 代码生成
npm run generate:component
npm run generate:api

# 代码检查
npm run lint
npm run type-check

# 测试运行
npm run test
npm run test:coverage

# 构建部署
npm run build
npm run deploy
```

### 项目管理
- **Jira** - 任务跟踪
- **Trello** - 看板管理
- **GitHub Projects** - 项目规划
- **Notion** - 文档协作

## 📊 Progress Tracking - 进度跟踪

### 进度指标
- **完成度** - 已完成/总任务数
- **质量指标** - 缺陷密度、覆盖率
- **效率指标** - 速度、生产力
- **风险指标** - 风险数量和等级

### 报告机制
- **日报** - 每日进展和问题
- **周报** - 周度总结和计划
- **里程碑报告** - 阶段性成果
- **项目总结** - 经验教训

## 📋 Checklist - 检查清单

### 开始前
- [ ] 需求理解清晰
- [ ] 技术方案确定
- [ ] 资源配置到位
- [ ] 环境准备就绪

### 实现中
- [ ] 按计划执行
- [ ] 质量标准遵循
- [ ] 进度及时汇报
- [ ] 风险主动管理

### 完成后
- [ ] 功能测试通过
- [ ] 代码审查完成
- [ ] 文档更新完整
- [ ] 部署验证成功
